"""
Utility functions for the Finance Backtesting System
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from typing import Dict, <PERSON><PERSON>

def calculate_performance_metrics(df: pd.DataFrame, initial_capital: float) -> Dict:
    """
    Calculate comprehensive performance metrics.
    
    Args:
        df (pd.DataFrame): DataFrame with trading results
        initial_capital (float): Initial capital amount
        
    Returns:
        dict: Performance metrics
    """
    if len(df) == 0 or 'portfolio_value' not in df.columns:
        return {}
    
    # Basic metrics
    final_value = df['portfolio_value'].iloc[-1]
    total_return = (final_value - initial_capital) / initial_capital
    
    # Calculate returns for risk metrics
    returns = df['returns'].dropna()
    if len(returns) == 0:
        return {'total_return': total_return, 'final_value': final_value}
    
    # Annualized metrics (assuming 252 trading days per year)
    trading_days = len(df)
    years = trading_days / (252 * 24 * 4)  # 15-minute intervals
    
    annualized_return = (1 + total_return) ** (1/years) - 1 if years > 0 else 0
    
    # Volatility (annualized)
    volatility = returns.std() * np.sqrt(252 * 24 * 4) if len(returns) > 1 else 0
    
    # Sharpe ratio (assuming 0% risk-free rate)
    sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
    
    # Maximum drawdown
    rolling_max = df['portfolio_value'].expanding().max()
    drawdown = (df['portfolio_value'] - rolling_max) / rolling_max
    max_drawdown = drawdown.min()
    
    # Win rate
    winning_trades = (returns > 0).sum()
    total_trades = len(returns)
    win_rate = winning_trades / total_trades if total_trades > 0 else 0
    
    # Average win/loss
    winning_returns = returns[returns > 0]
    losing_returns = returns[returns < 0]
    
    avg_win = winning_returns.mean() if len(winning_returns) > 0 else 0
    avg_loss = losing_returns.mean() if len(losing_returns) > 0 else 0
    
    # Profit factor
    total_wins = winning_returns.sum() if len(winning_returns) > 0 else 0
    total_losses = abs(losing_returns.sum()) if len(losing_returns) > 0 else 0
    profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
    
    return {
        'total_return': total_return,
        'annualized_return': annualized_return,
        'volatility': volatility,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'final_value': final_value,
        'win_rate': win_rate,
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'losing_trades': total_trades - winning_trades,
        'avg_win': avg_win,
        'avg_loss': avg_loss,
        'profit_factor': profit_factor,
        'trading_period_years': years
    }

def create_visualization(df: pd.DataFrame, 
                        output_path: str = 'backtest_results.png',
                        figsize: Tuple[int, int] = (15, 12),
                        sample_points: int = 5000) -> None:
    """
    Create comprehensive visualization with 3 subplots.
    
    Args:
        df (pd.DataFrame): Results DataFrame
        output_path (str): Path to save the PNG file
        figsize (tuple): Figure size (width, height)
        sample_points (int): Number of points to sample for visualization
    """
    if df is None or len(df) == 0:
        raise ValueError("No data to visualize.")
    
    # Sample data if too large for visualization
    if len(df) > sample_points:
        step = len(df) // sample_points
        df = df.iloc[::step].copy()
    
    # Create figure with subplots
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=figsize, sharex=True)
    fig.suptitle('Finance Strategy Backtesting Results', fontsize=16, fontweight='bold')
    
    # Subplot 1: Price Chart with Signals
    ax1.plot(df.index, df['close'], label='Close Price', color='black', linewidth=1)
    
    # Plot buy signals (green up arrows)
    buy_signals = df[df['buy_signal'] == True]
    if len(buy_signals) > 0:
        ax1.scatter(buy_signals.index, buy_signals['close'], 
                   marker='^', color='green', s=50, label='Buy Signal', zorder=5)
    
    # Plot sell signals (red down arrows)
    sell_signals = df[df['sell_signal'] == True]
    if len(sell_signals) > 0:
        ax1.scatter(sell_signals.index, sell_signals['close'], 
                   marker='v', color='red', s=50, label='Sell Signal', zorder=5)
    
    ax1.set_ylabel('Price')
    ax1.set_title('Stock Price with Trading Signals')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Subplot 2: Strategy Scores
    ax2.plot(df.index, df['rsi_score'], label='RSI Score', color='blue', alpha=0.7)
    ax2.plot(df.index, df['bb_score'], label='Bollinger Score', color='orange', alpha=0.7)
    ax2.plot(df.index, df['obv_score'], label='OBV Score', color='purple', alpha=0.7)
    ax2.plot(df.index, df['composite_score'], label='Composite Score', color='red', linewidth=2)
    
    # Add threshold lines
    ax2.axhline(y=30, color='green', linestyle='--', alpha=0.5, label='Buy Threshold')
    ax2.axhline(y=70, color='red', linestyle='--', alpha=0.5, label='Sell Threshold')
    
    ax2.set_ylabel('Score')
    ax2.set_title('Strategy Scores Over Time')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 100)
    
    # Subplot 3: Equity Curve
    ax3.plot(df.index, df['portfolio_value'], label='Portfolio Value', color='green', linewidth=2)
    ax3.axhline(y=df['portfolio_value'].iloc[0], color='gray', linestyle='--', alpha=0.5, label='Initial Capital')
    
    ax3.set_ylabel('Portfolio Value ($)')
    ax3.set_xlabel('Date')
    ax3.set_title('Portfolio Equity Curve')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # Format x-axis
    ax3.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    ax3.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
    plt.setp(ax3.xaxis.get_majorticklabels(), rotation=45)
    
    # Adjust layout and save
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Visualization saved to: {output_path}")

def print_performance_summary(performance_metrics: Dict) -> None:
    """Print a formatted summary of performance metrics."""
    if not performance_metrics:
        print("No performance metrics available.")
        return
    
    metrics = performance_metrics
    
    print("\n" + "="*60)
    print("BACKTESTING PERFORMANCE SUMMARY")
    print("="*60)
    
    print(f"Total Return: {metrics['total_return']:.2%}")
    print(f"Annualized Return: {metrics['annualized_return']:.2%}")
    print(f"Volatility (Annualized): {metrics['volatility']:.2%}")
    print(f"Sharpe Ratio: {metrics['sharpe_ratio']:.3f}")
    print(f"Maximum Drawdown: {metrics['max_drawdown']:.2%}")
    print(f"Final Portfolio Value: ${metrics['final_value']:,.2f}")
    
    print(f"\nTrading Statistics:")
    print(f"Total Trades: {metrics['total_trades']}")
    print(f"Winning Trades: {metrics['winning_trades']}")
    print(f"Losing Trades: {metrics['losing_trades']}")
    print(f"Win Rate: {metrics['win_rate']:.2%}")
    print(f"Average Win: {metrics['avg_win']:.4f}")
    print(f"Average Loss: {metrics['avg_loss']:.4f}")
    print(f"Profit Factor: {metrics['profit_factor']:.3f}")
    print(f"Trading Period: {metrics['trading_period_years']:.2f} years")
    
    print("="*60)
