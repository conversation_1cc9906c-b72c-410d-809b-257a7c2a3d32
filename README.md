# Finance Strategy Backtesting System

A comprehensive Python backtesting framework for finance strategies using technical indicators (RSI, Bollinger Bands, OBV) with weighted scoring.

## Features

- **Multi-Indicator Strategy**: Combines RSI, Bollinger Bands, and OBV indicators
- **Weighted Scoring System**: Configurable weights for each technical indicator
- **Comprehensive Backtesting**: Full trading simulation with position management
- **Performance Metrics**: Detailed performance analysis including Sharpe ratio, drawdown, etc.
- **Visualization**: Professional charts with price, signals, scores, and equity curve
- **Flexible Parameters**: Highly configurable for strategy optimization

## Installation

### Requirements

```bash
pip install pandas pandas-ta matplotlib numpy
```

### Files

- `finance_backtesting.py` - Main backtesting system
- `example_usage.py` - Usage examples and demonstrations
- `README.md` - This documentation

## Quick Start

```python
from finance_backtesting import FinanceBacktester

# Initialize backtester
backtester = FinanceBacktester()

# Run backtest with default parameters
results = backtester.backtest_strategy('your_data.csv')

# Print performance summary
backtester.print_performance_summary()

# Create visualization
backtester.create_visualization('results.png')
```

## Data Format

The system expects CSV files with the following columns:
- `datetime` - Format: yyyy-mm-dd hh:mm:ss
- `open` - Opening price
- `high` - High price
- `low` - Low price
- `close` - Closing price
- `volume` - Trading volume

Example:
```csv
datetime,open,high,low,close,volume
2020-01-01 09:00:00,100.0,101.0,99.5,100.5,1000
2020-01-01 09:15:00,100.5,102.0,100.0,101.5,1200
```

## Parameters

### Technical Indicators

#### RSI (Relative Strength Index)
- `rsi_window` (int, default=14): Number of periods for RSI calculation
- `RSI_oversoldLevel` (float, default=30): RSI threshold for oversold condition
- `RSI_overboughtLevel` (float, default=70): RSI threshold for overbought condition

#### Bollinger Bands
- `bollinger_window` (int, default=20): Number of periods for calculation
- `bollinger_std_dev` (float, default=2.0): Standard deviation multiplier

#### OBV (On-Balance Volume)
- `obv_window` (int, default=10): Number of periods for OBV moving average
- `obv_threshold` (float, default=0.1): Threshold for OBV signal generation

### Strategy Configuration

#### Weights
- `strategy_weights` (dict): Weights for each strategy component
  - Default: `{'rsi': 0.4, 'bollinger': 0.4, 'obv': 0.2}`
  - Must sum to 1.0 (auto-normalized if not)

#### Trading Parameters
- `initial_capital` (float, default=100000): Starting capital
- `position_size` (float, default=0.95): Fraction of capital to use per trade
- `commission` (float, default=0.001): Commission rate per trade

## Strategy Logic

### Scoring System

1. **RSI Strategy Score**:
   - Score = 100 when RSI > overbought level (sell signal)
   - Score = 0 when RSI < oversold level (buy signal)
   - Linear interpolation between thresholds

2. **Bollinger Bands Score**:
   - Score = 100 when price > upper band (sell signal)
   - Score = 0 when price < lower band (buy signal)
   - Score = 50 when price is between bands

3. **OBV Strategy Score**:
   - Based on OBV momentum relative to its moving average
   - Score = 100 when momentum > threshold (sell signal)
   - Score = 0 when momentum < -threshold (buy signal)
   - Score = 50 for neutral momentum

### Signal Generation

- **Composite Score** = Weighted sum of individual scores
- **BUY Signal**: Composite score < 30
- **SELL Signal**: Composite score > 70

## Performance Metrics

The system calculates comprehensive performance metrics:

- **Total Return**: Overall portfolio return
- **Annualized Return**: Return adjusted for time period
- **Volatility**: Annualized standard deviation of returns
- **Sharpe Ratio**: Risk-adjusted return measure
- **Maximum Drawdown**: Largest peak-to-trough decline
- **Win Rate**: Percentage of profitable trades
- **Profit Factor**: Ratio of total wins to total losses
- **Average Win/Loss**: Mean return of winning/losing trades

## Visualization

The system generates a comprehensive 3-subplot visualization:

1. **Price Chart**: Stock price with buy/sell signal markers
2. **Strategy Scores**: Individual and composite scores over time
3. **Equity Curve**: Portfolio value progression

## Usage Examples

### Basic Usage

```python
from finance_backtesting import FinanceBacktester

backtester = FinanceBacktester()
results = backtester.backtest_strategy('data.csv')
backtester.print_performance_summary()
backtester.create_visualization()
```

### Custom Parameters

```python
custom_weights = {'rsi': 0.5, 'bollinger': 0.3, 'obv': 0.2}

results = backtester.backtest_strategy(
    csv_path='data.csv',
    rsi_window=21,
    RSI_oversoldLevel=25,
    RSI_overboughtLevel=75,
    bollinger_window=30,
    bollinger_std_dev=2.5,
    strategy_weights=custom_weights,
    initial_capital=200000,
    position_size=0.8,
    commission=0.0015
)
```

### Parameter Optimization

```python
# Test different RSI windows
for rsi_window in [10, 14, 21, 30]:
    backtester = FinanceBacktester()
    results = backtester.backtest_strategy(
        'data.csv', 
        rsi_window=rsi_window
    )
    print(f"RSI {rsi_window}: Return = {results['performance_metrics']['total_return']:.2%}")
```

## Error Handling

The system includes comprehensive error handling for:
- Missing or invalid data files
- Incorrect data formats
- Invalid parameter values
- Calculation errors

## Best Practices

1. **Data Quality**: Ensure clean, consistent data with proper datetime formatting
2. **Parameter Testing**: Test different parameter combinations for optimization
3. **Risk Management**: Use appropriate position sizing and commission rates
4. **Validation**: Validate results with out-of-sample testing
5. **Documentation**: Keep track of parameter combinations and results

## Limitations

- Assumes perfect execution (no slippage beyond commission)
- Does not account for market impact
- Simplified position sizing (fixed percentage)
- No stop-loss or take-profit mechanisms
- Assumes continuous market operation

## Contributing

To extend the system:
1. Add new technical indicators in `calculate_technical_indicators()`
2. Implement new scoring methods in `calculate_strategy_scores()`
3. Enhance visualization in `create_visualization()`
4. Add new performance metrics in `calculate_performance_metrics()`

## License

This project is provided as-is for educational and research purposes.
