"""
Comprehensive Finance Strategy Backtesting System (Manual Indicators)

This module provides a complete backtesting framework for finance strategies
using manually implemented technical indicators to avoid dependency issues.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from typing import Dict, Tuple, Optional, Union
import warnings
from datetime import datetime
import os
from tqdm import tqdm

warnings.filterwarnings('ignore')


class FinanceBacktester:
    """
    A comprehensive backtesting system for finance strategies using technical indicators.

    This class implements a weighted scoring system combining RSI, Bollinger Bands, and OBV
    indicators to generate trading signals and backtest strategy performance.
    """

    def __init__(self):
        """Initialize the backtester with default settings."""
        self.data = None
        self.results = None
        self.signals = None
        self.performance_metrics = {}
        self.results_folder = "results"
        self._create_results_folder()

    def _create_results_folder(self):
        """Create results folder if it doesn't exist."""
        if not os.path.exists(self.results_folder):
            os.makedirs(self.results_folder)
            print(f"Created results folder: {self.results_folder}")

    def load_data(self, csv_path: str) -> pd.DataFrame:
        """
        Load and validate CSV data with proper datetime parsing.

        Args:
            csv_path (str): Path to CSV file containing OHLCV data

        Returns:
            pd.DataFrame: Validated DataFrame with datetime index

        Raises:
            FileNotFoundError: If CSV file doesn't exist
            ValueError: If data format is invalid
        """
        if not os.path.exists(csv_path):
            raise FileNotFoundError(f"CSV file not found: {csv_path}")

        try:
            # Load CSV data
            df = pd.read_csv(csv_path)

            # Validate required columns
            required_columns = ['datetime', 'open',
                                'high', 'low', 'close', 'volume']
            missing_columns = [
                col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(
                    f"Missing required columns: {missing_columns}")

            # Convert datetime column
            df['datetime'] = pd.to_datetime(df['datetime'])
            df.set_index('datetime', inplace=True)

            # Validate data types and ranges
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            # Check for missing values
            if df.isnull().any().any():
                print("Warning: Found missing values in data. Forward filling...")
                df.fillna(method='ffill', inplace=True)

            # Validate OHLC relationships
            invalid_ohlc = (df['high'] < df['low']) | (df['high'] < df['open']) | \
                (df['high'] < df['close']) | (df['low'] > df['open']) | \
                (df['low'] > df['close'])

            if invalid_ohlc.any():
                print(
                    f"Warning: Found {invalid_ohlc.sum()} rows with invalid OHLC relationships")

            # Sort by datetime
            df.sort_index(inplace=True)

            print(
                f"Successfully loaded {len(df)} rows of data from {df.index[0]} to {df.index[-1]}")
            self.data = df
            return df

        except Exception as e:
            raise ValueError(f"Error loading data: {str(e)}")

    def calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """
        Calculate Relative Strength Index (RSI).

        Args:
            prices (pd.Series): Price series
            window (int): RSI calculation window

        Returns:
            pd.Series: RSI values
        """
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()

        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def calculate_bollinger_bands(self, prices: pd.Series, window: int = 20, std_dev: float = 2.0) -> Dict[str, pd.Series]:
        """
        Calculate Bollinger Bands.

        Args:
            prices (pd.Series): Price series
            window (int): Moving average window
            std_dev (float): Standard deviation multiplier

        Returns:
            dict: Dictionary with upper, middle, and lower bands
        """
        sma = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()

        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)

        return {
            'upper': upper_band,
            'middle': sma,
            'lower': lower_band
        }

    def calculate_obv(self, prices: pd.Series, volume: pd.Series) -> pd.Series:
        """
        Calculate On-Balance Volume (OBV).

        Args:
            prices (pd.Series): Price series
            volume (pd.Series): Volume series

        Returns:
            pd.Series: OBV values
        """
        price_change = prices.diff()
        obv = np.where(price_change > 0, volume,
                       np.where(price_change < 0, -volume, 0))
        return pd.Series(obv, index=prices.index).cumsum()

    def calculate_technical_indicators(self,
                                       rsi_window: int = 14,
                                       bollinger_window: int = 20,
                                       bollinger_std_dev: float = 2.0,
                                       obv_window: int = 10) -> pd.DataFrame:
        """
        Calculate technical indicators manually.

        Args:
            rsi_window (int): RSI calculation window
            bollinger_window (int): Bollinger Bands window
            bollinger_std_dev (float): Bollinger Bands standard deviation multiplier
            obv_window (int): OBV moving average window

        Returns:
            pd.DataFrame: DataFrame with technical indicators
        """
        if self.data is None:
            raise ValueError("No data loaded. Call load_data() first.")

        df = self.data.copy()

        print("Calculating technical indicators...")
        with tqdm(total=4, desc="Technical Indicators") as pbar:
            # Calculate RSI
            pbar.set_description("Calculating RSI")
            df['rsi'] = self.calculate_rsi(df['close'], rsi_window)
            pbar.update(1)

            # Calculate Bollinger Bands
            pbar.set_description("Calculating Bollinger Bands")
            bb = self.calculate_bollinger_bands(
                df['close'], bollinger_window, bollinger_std_dev)
            df['bb_lower'] = bb['lower']
            df['bb_middle'] = bb['middle']
            df['bb_upper'] = bb['upper']
            pbar.update(1)

            # Calculate OBV
            pbar.set_description("Calculating OBV")
            df['obv'] = self.calculate_obv(df['close'], df['volume'])
            pbar.update(1)

            # Calculate OBV moving average
            pbar.set_description("Calculating OBV MA")
            df['obv_ma'] = df['obv'].rolling(window=obv_window).mean()
            pbar.update(1)

        return df

    def calculate_strategy_scores(self,
                                  df: pd.DataFrame,
                                  RSI_oversoldLevel: float = 30,
                                  RSI_overboughtLevel: float = 70,
                                  obv_threshold: float = 0.1) -> pd.DataFrame:
        """
        Calculate individual strategy scores for RSI, Bollinger Bands, and OBV.

        Args:
            df (pd.DataFrame): DataFrame with technical indicators
            RSI_oversoldLevel (float): RSI oversold threshold
            RSI_overboughtLevel (float): RSI overbought threshold
            obv_threshold (float): OBV momentum threshold

        Returns:
            pd.DataFrame: DataFrame with strategy scores
        """
        # RSI Strategy Score
        def calculate_rsi_score(rsi_values):
            scores = np.where(rsi_values > RSI_overboughtLevel, 100,
                              np.where(rsi_values < RSI_oversoldLevel, 0,
                                       ((rsi_values - RSI_oversoldLevel) /
                                        (RSI_overboughtLevel - RSI_oversoldLevel)) * 100))
            return scores

        df['rsi_score'] = calculate_rsi_score(df['rsi'])

        # Bollinger Bands Strategy Score
        def calculate_bb_score(close, bb_lower, bb_upper):
            scores = np.where(close > bb_upper, 100,
                              np.where(close < bb_lower, 0, 50))
            return scores

        df['bb_score'] = calculate_bb_score(
            df['close'], df['bb_lower'], df['bb_upper'])

        # OBV Strategy Score
        def calculate_obv_score(obv, obv_ma, threshold):
            obv_momentum = (obv - obv_ma) / obv_ma
            scores = np.where(obv_momentum > threshold, 100,
                              np.where(obv_momentum < -threshold, 0, 50))
            return scores

        # Handle division by zero in OBV calculation
        obv_ma_safe = df['obv_ma'].replace(0, np.nan)
        df['obv_score'] = calculate_obv_score(
            df['obv'], obv_ma_safe, obv_threshold)
        df['obv_score'].fillna(50, inplace=True)  # Default to neutral score

        return df

    def generate_trading_signals(self,
                                 df: pd.DataFrame,
                                 strategy_weights: Dict[str, float] = None,
                                 buy_threshold: float = 30,
                                 sell_threshold: float = 70) -> pd.DataFrame:
        """
        Generate trading signals based on weighted composite scores.

        Args:
            df (pd.DataFrame): DataFrame with strategy scores
            strategy_weights (dict): Weights for each strategy component
            buy_threshold (float): Threshold for buy signals
            sell_threshold (float): Threshold for sell signals

        Returns:
            pd.DataFrame: DataFrame with trading signals
        """
        if strategy_weights is None:
            strategy_weights = {'rsi': 0.4, 'bollinger': 0.4, 'obv': 0.2}

        # Validate weights sum to 1
        total_weight = sum(strategy_weights.values())
        if abs(total_weight - 1.0) > 0.001:
            print(
                f"Warning: Strategy weights sum to {total_weight}, normalizing...")
            strategy_weights = {k: v/total_weight for k,
                                v in strategy_weights.items()}

        # Calculate weighted composite score
        df['composite_score'] = (
            df['rsi_score'] * strategy_weights.get('rsi', 0) +
            df['bb_score'] * strategy_weights.get('bollinger', 0) +
            df['obv_score'] * strategy_weights.get('obv', 0)
        )

        # Generate signals
        df['signal'] = 0  # 0: hold, 1: buy, -1: sell
        df['signal'] = np.where(df['composite_score'] < buy_threshold, 1,
                                np.where(df['composite_score'] > sell_threshold, -1, 0))

        # Generate position changes (entry/exit points)
        df['position_change'] = df['signal'].diff()
        df['buy_signal'] = (df['position_change'] == 1) | (
            (df['signal'] == 1) & (df['signal'].shift(1) == 0))
        df['sell_signal'] = (df['position_change'] == -
                             1) | ((df['signal'] == -1) & (df['signal'].shift(1) == 0))

        return df

    def simulate_trading(self,
                         df: pd.DataFrame,
                         initial_capital: float = 100000,
                         position_size: float = 0.95,
                         commission: float = 0.001) -> pd.DataFrame:
        """
        Simulate trading based on generated signals.

        Args:
            df (pd.DataFrame): DataFrame with trading signals
            initial_capital (float): Starting capital
            position_size (float): Fraction of capital to use per trade
            commission (float): Commission rate per trade

        Returns:
            pd.DataFrame: DataFrame with trading simulation results
        """
        df = df.copy()

        # Initialize trading variables
        # Current position (0: no position, 1: long, -1: short)
        df['position'] = 0
        df['cash'] = initial_capital
        df['holdings'] = 0.0
        df['portfolio_value'] = initial_capital
        df['returns'] = 0.0
        df['cumulative_returns'] = 0.0

        cash = initial_capital
        position = 0
        shares = 0

        print("Simulating trading...")
        for i in tqdm(range(1, len(df)), desc="Trading Simulation"):
            current_price = df['close'].iloc[i]

            # Check for buy signal
            if df['buy_signal'].iloc[i] and position <= 0:
                if position < 0:  # Close short position first
                    cash += shares * current_price * (1 - commission)
                    shares = 0

                # Open long position
                trade_value = cash * position_size
                shares = trade_value / (current_price * (1 + commission))
                cash -= trade_value
                position = 1

            # Check for sell signal
            elif df['sell_signal'].iloc[i] and position >= 0:
                if position > 0:  # Close long position first
                    cash += shares * current_price * (1 - commission)
                    shares = 0

                # Open short position
                trade_value = cash * position_size
                shares = -trade_value / (current_price * (1 + commission))
                cash += trade_value
                position = -1

            # Update portfolio values
            df.loc[df.index[i], 'position'] = position
            df.loc[df.index[i], 'cash'] = cash
            df.loc[df.index[i], 'holdings'] = shares * current_price
            df.loc[df.index[i], 'portfolio_value'] = cash + \
                shares * current_price

            # Calculate returns
            portfolio_return = (df['portfolio_value'].iloc[i] -
                                df['portfolio_value'].iloc[i-1]) / df['portfolio_value'].iloc[i-1]
            df.loc[df.index[i], 'returns'] = portfolio_return
            df.loc[df.index[i], 'cumulative_returns'] = (
                df['portfolio_value'].iloc[i] - initial_capital) / initial_capital

        return df

    def backtest_strategy(self,
                          csv_path: str,
                          rsi_window: int = 14,
                          RSI_oversoldLevel: float = 30,
                          RSI_overboughtLevel: float = 70,
                          bollinger_window: int = 20,
                          bollinger_std_dev: float = 2.0,
                          obv_window: int = 10,
                          obv_threshold: float = 0.1,
                          strategy_weights: Dict[str, float] = None,
                          initial_capital: float = 100000,
                          position_size: float = 0.95,
                          commission: float = 0.001) -> Dict:
        """
        Main backtesting function that orchestrates the entire process.

        Args:
            csv_path (str): Path to CSV data file
            rsi_window (int): RSI calculation window
            RSI_oversoldLevel (float): RSI oversold threshold
            RSI_overboughtLevel (float): RSI overbought threshold
            bollinger_window (int): Bollinger Bands window
            bollinger_std_dev (float): Bollinger Bands standard deviation
            obv_window (int): OBV moving average window
            obv_threshold (float): OBV momentum threshold
            strategy_weights (dict): Strategy component weights
            initial_capital (float): Starting capital
            position_size (float): Fraction of capital to use per trade
            commission (float): Commission rate per trade

        Returns:
            dict: Comprehensive backtesting results
        """
        from backtesting_utils import calculate_performance_metrics

        # Load and prepare data
        df = self.load_data(csv_path)

        # Calculate technical indicators
        df = self.calculate_technical_indicators(
            rsi_window=rsi_window,
            bollinger_window=bollinger_window,
            bollinger_std_dev=bollinger_std_dev,
            obv_window=obv_window
        )

        # Calculate strategy scores
        df = self.calculate_strategy_scores(
            df,
            RSI_oversoldLevel=RSI_oversoldLevel,
            RSI_overboughtLevel=RSI_overboughtLevel,
            obv_threshold=obv_threshold
        )

        # Generate trading signals
        df = self.generate_trading_signals(
            df,
            strategy_weights=strategy_weights
        )

        # Simulate trading
        df = self.simulate_trading(
            df, initial_capital, position_size, commission)

        # Calculate performance metrics
        performance_metrics = calculate_performance_metrics(
            df, initial_capital)

        # Store results
        self.results = df
        self.performance_metrics = performance_metrics

        return {
            'data': df,
            'performance_metrics': performance_metrics,
            'parameters': {
                'rsi_window': rsi_window,
                'RSI_oversoldLevel': RSI_oversoldLevel,
                'RSI_overboughtLevel': RSI_overboughtLevel,
                'bollinger_window': bollinger_window,
                'bollinger_std_dev': bollinger_std_dev,
                'obv_window': obv_window,
                'obv_threshold': obv_threshold,
                'strategy_weights': strategy_weights or {'rsi': 0.4, 'bollinger': 0.4, 'obv': 0.2},
                'initial_capital': initial_capital,
                'position_size': position_size,
                'commission': commission
            }
        }

    def create_visualization(self,
                             output_path: str = 'backtest_results.png',
                             figsize: Tuple[int, int] = (15, 12),
                             sample_points: int = 5000) -> None:
        """Create visualization using utility function."""
        from backtesting_utils import create_visualization
        if self.results is None:
            raise ValueError(
                "No results to visualize. Run backtest_strategy() first.")

        # Ensure output path is in results folder
        if not output_path.startswith(self.results_folder):
            output_path = os.path.join(self.results_folder, output_path)

        create_visualization(self.results, output_path, figsize, sample_points)

    def print_performance_summary(self) -> None:
        """Print performance summary using utility function."""
        from backtesting_utils import print_performance_summary
        print_performance_summary(self.performance_metrics)

    def save_results_to_csv(self, filename: str = 'backtest_results.csv') -> None:
        """Save backtesting results to CSV file in results folder."""
        if self.results is None:
            raise ValueError(
                "No results to save. Run backtest_strategy() first.")

        # Ensure filename is in results folder
        if not filename.startswith(self.results_folder):
            filepath = os.path.join(self.results_folder, filename)
        else:
            filepath = filename

        # Save results to CSV
        self.results.to_csv(filepath)
        print(f"Results saved to: {filepath}")

    def save_performance_metrics(self, filename: str = 'performance_metrics.txt') -> None:
        """Save performance metrics to text file in results folder."""
        if not self.performance_metrics:
            raise ValueError(
                "No performance metrics to save. Run backtest_strategy() first.")

        # Ensure filename is in results folder
        if not filename.startswith(self.results_folder):
            filepath = os.path.join(self.results_folder, filename)
        else:
            filepath = filename

        # Save performance metrics to text file
        with open(filepath, 'w') as f:
            f.write("BACKTESTING PERFORMANCE SUMMARY\n")
            f.write("="*60 + "\n\n")

            metrics = self.performance_metrics
            f.write(f"Total Return: {metrics['total_return']:.2%}\n")
            f.write(f"Annualized Return: {metrics['annualized_return']:.2%}\n")
            f.write(f"Volatility (Annualized): {metrics['volatility']:.2%}\n")
            f.write(f"Sharpe Ratio: {metrics['sharpe_ratio']:.3f}\n")
            f.write(f"Maximum Drawdown: {metrics['max_drawdown']:.2%}\n")
            f.write(
                f"Final Portfolio Value: ${metrics['final_value']:,.2f}\n\n")

            f.write("Trading Statistics:\n")
            f.write(f"Total Trades: {metrics['total_trades']}\n")
            f.write(f"Winning Trades: {metrics['winning_trades']}\n")
            f.write(f"Losing Trades: {metrics['losing_trades']}\n")
            f.write(f"Win Rate: {metrics['win_rate']:.2%}\n")
            f.write(f"Average Win: {metrics['avg_win']:.4f}\n")
            f.write(f"Average Loss: {metrics['avg_loss']:.4f}\n")
            f.write(f"Profit Factor: {metrics['profit_factor']:.3f}\n")
            f.write(
                f"Trading Period: {metrics['trading_period_years']:.2f} years\n")

        print(f"Performance metrics saved to: {filepath}")
