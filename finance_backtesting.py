"""
Comprehensive Finance Strategy Backtesting System

This module provides a complete backtesting framework for finance strategies
using technical indicators (RSI, Bollinger Bands, OBV) with weighted scoring.
"""

import pandas as pd
import pandas_ta as ta
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from typing import Dict, Tuple, Optional, Union
import warnings
from datetime import datetime
import os

warnings.filterwarnings('ignore')


class FinanceBacktester:
    """
    A comprehensive backtesting system for finance strategies using technical indicators.

    This class implements a weighted scoring system combining RSI, Bollinger Bands, and OBV
    indicators to generate trading signals and backtest strategy performance.
    """

    def __init__(self):
        """Initialize the backtester with default settings."""
        self.data = None
        self.results = None
        self.signals = None
        self.performance_metrics = {}

    def load_data(self, csv_path: str) -> pd.DataFrame:
        """
        Load and validate CSV data with proper datetime parsing.

        Args:
            csv_path (str): Path to CSV file containing OHLCV data

        Returns:
            pd.DataFrame: Validated DataFrame with datetime index

        Raises:
            FileNotFoundError: If CSV file doesn't exist
            ValueError: If data format is invalid
        """
        if not os.path.exists(csv_path):
            raise FileNotFoundError(f"CSV file not found: {csv_path}")

        try:
            # Load CSV data
            df = pd.read_csv(csv_path)

            # Validate required columns
            required_columns = ['datetime', 'open',
                                'high', 'low', 'close', 'volume']
            missing_columns = [
                col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(
                    f"Missing required columns: {missing_columns}")

            # Convert datetime column
            df['datetime'] = pd.to_datetime(df['datetime'])
            df.set_index('datetime', inplace=True)

            # Validate data types and ranges
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            # Check for missing values
            if df.isnull().any().any():
                print("Warning: Found missing values in data. Forward filling...")
                df.fillna(method='ffill', inplace=True)

            # Validate OHLC relationships
            invalid_ohlc = (df['high'] < df['low']) | (df['high'] < df['open']) | \
                (df['high'] < df['close']) | (df['low'] > df['open']) | \
                (df['low'] > df['close'])

            if invalid_ohlc.any():
                print(
                    f"Warning: Found {invalid_ohlc.sum()} rows with invalid OHLC relationships")

            # Sort by datetime
            df.sort_index(inplace=True)

            print(
                f"Successfully loaded {len(df)} rows of data from {df.index[0]} to {df.index[-1]}")
            self.data = df
            return df

        except Exception as e:
            raise ValueError(f"Error loading data: {str(e)}")

    def calculate_technical_indicators(self,
                                       rsi_window: int = 14,
                                       bollinger_window: int = 20,
                                       bollinger_std_dev: float = 2.0,
                                       obv_window: int = 10) -> pd.DataFrame:
        """
        Calculate technical indicators using pandas-ta.

        Args:
            rsi_window (int): RSI calculation window
            bollinger_window (int): Bollinger Bands window
            bollinger_std_dev (float): Bollinger Bands standard deviation multiplier
            obv_window (int): OBV moving average window

        Returns:
            pd.DataFrame: DataFrame with technical indicators
        """
        if self.data is None:
            raise ValueError("No data loaded. Call load_data() first.")

        df = self.data.copy()

        # Calculate RSI
        df['rsi'] = ta.rsi(df['close'], length=rsi_window)

        # Calculate Bollinger Bands
        bb = ta.bbands(df['close'], length=bollinger_window,
                       std=bollinger_std_dev)
        df['bb_lower'] = bb[f'BBL_{bollinger_window}_{bollinger_std_dev}']
        df['bb_middle'] = bb[f'BBM_{bollinger_window}_{bollinger_std_dev}']
        df['bb_upper'] = bb[f'BBU_{bollinger_window}_{bollinger_std_dev}']

        # Calculate OBV and its moving average
        df['obv'] = ta.obv(df['close'], df['volume'])
        df['obv_ma'] = ta.sma(df['obv'], length=obv_window)

        return df

    def calculate_strategy_scores(self,
                                  df: pd.DataFrame,
                                  RSI_oversoldLevel: float = 30,
                                  RSI_overboughtLevel: float = 70,
                                  obv_threshold: float = 0.1) -> pd.DataFrame:
        """
        Calculate individual strategy scores for RSI, Bollinger Bands, and OBV.

        Args:
            df (pd.DataFrame): DataFrame with technical indicators
            RSI_oversoldLevel (float): RSI oversold threshold
            RSI_overboughtLevel (float): RSI overbought threshold
            obv_threshold (float): OBV momentum threshold

        Returns:
            pd.DataFrame: DataFrame with strategy scores
        """
        # RSI Strategy Score
        def calculate_rsi_score(rsi_values):
            scores = np.where(rsi_values > RSI_overboughtLevel, 100,
                              np.where(rsi_values < RSI_oversoldLevel, 0,
                                       ((rsi_values - RSI_oversoldLevel) /
                                        (RSI_overboughtLevel - RSI_oversoldLevel)) * 100))
            return scores

        df['rsi_score'] = calculate_rsi_score(df['rsi'])

        # Bollinger Bands Strategy Score
        def calculate_bb_score(close, bb_lower, bb_upper):
            scores = np.where(close > bb_upper, 100,
                              np.where(close < bb_lower, 0, 50))
            return scores

        df['bb_score'] = calculate_bb_score(
            df['close'], df['bb_lower'], df['bb_upper'])

        # OBV Strategy Score
        def calculate_obv_score(obv, obv_ma, threshold):
            obv_momentum = (obv - obv_ma) / obv_ma
            scores = np.where(obv_momentum > threshold, 100,
                              np.where(obv_momentum < -threshold, 0, 50))
            return scores

        # Handle division by zero in OBV calculation
        obv_ma_safe = df['obv_ma'].replace(0, np.nan)
        df['obv_score'] = calculate_obv_score(
            df['obv'], obv_ma_safe, obv_threshold)
        df['obv_score'].fillna(50, inplace=True)  # Default to neutral score

        return df

    def generate_trading_signals(self,
                                 df: pd.DataFrame,
                                 strategy_weights: Dict[str, float] = None,
                                 buy_threshold: float = 30,
                                 sell_threshold: float = 70) -> pd.DataFrame:
        """
        Generate trading signals based on weighted composite scores.

        Args:
            df (pd.DataFrame): DataFrame with strategy scores
            strategy_weights (dict): Weights for each strategy component
            buy_threshold (float): Threshold for buy signals
            sell_threshold (float): Threshold for sell signals

        Returns:
            pd.DataFrame: DataFrame with trading signals
        """
        if strategy_weights is None:
            strategy_weights = {'rsi': 0.4, 'bollinger': 0.4, 'obv': 0.2}

        # Validate weights sum to 1
        total_weight = sum(strategy_weights.values())
        if abs(total_weight - 1.0) > 0.001:
            print(
                f"Warning: Strategy weights sum to {total_weight}, normalizing...")
            strategy_weights = {k: v/total_weight for k,
                                v in strategy_weights.items()}

        # Calculate weighted composite score
        df['composite_score'] = (
            df['rsi_score'] * strategy_weights.get('rsi', 0) +
            df['bb_score'] * strategy_weights.get('bollinger', 0) +
            df['obv_score'] * strategy_weights.get('obv', 0)
        )

        # Generate signals
        df['signal'] = 0  # 0: hold, 1: buy, -1: sell
        df['signal'] = np.where(df['composite_score'] < buy_threshold, 1,
                                np.where(df['composite_score'] > sell_threshold, -1, 0))

        # Generate position changes (entry/exit points)
        df['position_change'] = df['signal'].diff()
        df['buy_signal'] = (df['position_change'] == 1) | (
            (df['signal'] == 1) & (df['signal'].shift(1) == 0))
        df['sell_signal'] = (df['position_change'] == -
                             1) | ((df['signal'] == -1) & (df['signal'].shift(1) == 0))

        return df

    def simulate_trading(self,
                         df: pd.DataFrame,
                         initial_capital: float = 100000,
                         position_size: float = 0.95,
                         commission: float = 0.001) -> pd.DataFrame:
        """
        Simulate trading based on generated signals.

        Args:
            df (pd.DataFrame): DataFrame with trading signals
            initial_capital (float): Starting capital
            position_size (float): Fraction of capital to use per trade
            commission (float): Commission rate per trade

        Returns:
            pd.DataFrame: DataFrame with trading simulation results
        """
        df = df.copy()

        # Initialize trading variables
        # Current position (0: no position, 1: long, -1: short)
        df['position'] = 0
        df['cash'] = initial_capital
        df['holdings'] = 0.0
        df['portfolio_value'] = initial_capital
        df['returns'] = 0.0
        df['cumulative_returns'] = 0.0

        cash = initial_capital
        position = 0
        shares = 0

        for i in range(1, len(df)):
            current_price = df['close'].iloc[i]
            prev_price = df['close'].iloc[i-1]

            # Check for buy signal
            if df['buy_signal'].iloc[i] and position <= 0:
                if position < 0:  # Close short position first
                    cash += shares * current_price * (1 - commission)
                    shares = 0

                # Open long position
                trade_value = cash * position_size
                shares = trade_value / (current_price * (1 + commission))
                cash -= trade_value
                position = 1

            # Check for sell signal
            elif df['sell_signal'].iloc[i] and position >= 0:
                if position > 0:  # Close long position first
                    cash += shares * current_price * (1 - commission)
                    shares = 0

                # Open short position
                trade_value = cash * position_size
                shares = -trade_value / (current_price * (1 + commission))
                cash += trade_value
                position = -1

            # Update portfolio values
            df.loc[df.index[i], 'position'] = position
            df.loc[df.index[i], 'cash'] = cash
            df.loc[df.index[i], 'holdings'] = shares * current_price
            df.loc[df.index[i], 'portfolio_value'] = cash + \
                shares * current_price

            # Calculate returns
            portfolio_return = (df['portfolio_value'].iloc[i] -
                                df['portfolio_value'].iloc[i-1]) / df['portfolio_value'].iloc[i-1]
            df.loc[df.index[i], 'returns'] = portfolio_return
            df.loc[df.index[i], 'cumulative_returns'] = (
                df['portfolio_value'].iloc[i] - initial_capital) / initial_capital

        return df

    def calculate_performance_metrics(self, df: pd.DataFrame, initial_capital: float) -> Dict:
        """
        Calculate comprehensive performance metrics.

        Args:
            df (pd.DataFrame): DataFrame with trading results
            initial_capital (float): Initial capital amount

        Returns:
            dict: Performance metrics
        """
        if len(df) == 0 or 'portfolio_value' not in df.columns:
            return {}

        # Basic metrics
        final_value = df['portfolio_value'].iloc[-1]
        total_return = (final_value - initial_capital) / initial_capital

        # Calculate returns for risk metrics
        returns = df['returns'].dropna()
        if len(returns) == 0:
            return {'total_return': total_return, 'final_value': final_value}

        # Annualized metrics (assuming 252 trading days per year)
        trading_days = len(df)
        years = trading_days / (252 * 24 * 4)  # 15-minute intervals

        annualized_return = (1 + total_return) ** (1 /
                                                   years) - 1 if years > 0 else 0

        # Volatility (annualized)
        volatility = returns.std() * np.sqrt(252 * 24 * 4) if len(returns) > 1 else 0

        # Sharpe ratio (assuming 0% risk-free rate)
        sharpe_ratio = annualized_return / volatility if volatility > 0 else 0

        # Maximum drawdown
        rolling_max = df['portfolio_value'].expanding().max()
        drawdown = (df['portfolio_value'] - rolling_max) / rolling_max
        max_drawdown = drawdown.min()

        # Win rate
        winning_trades = (returns > 0).sum()
        total_trades = len(returns)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        # Average win/loss
        winning_returns = returns[returns > 0]
        losing_returns = returns[returns < 0]

        avg_win = winning_returns.mean() if len(winning_returns) > 0 else 0
        avg_loss = losing_returns.mean() if len(losing_returns) > 0 else 0

        # Profit factor
        total_wins = winning_returns.sum() if len(winning_returns) > 0 else 0
        total_losses = abs(losing_returns.sum()) if len(
            losing_returns) > 0 else 0
        profit_factor = total_wins / \
            total_losses if total_losses > 0 else float('inf')

        return {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'final_value': final_value,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': total_trades - winning_trades,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'trading_period_years': years
        }

    def backtest_strategy(self,
                          csv_path: str,
                          rsi_window: int = 14,
                          RSI_oversoldLevel: float = 30,
                          RSI_overboughtLevel: float = 70,
                          bollinger_window: int = 20,
                          bollinger_std_dev: float = 2.0,
                          obv_window: int = 10,
                          obv_threshold: float = 0.1,
                          strategy_weights: Dict[str, float] = None,
                          initial_capital: float = 100000,
                          position_size: float = 0.95,
                          commission: float = 0.001) -> Dict:
        """
        Main backtesting function that orchestrates the entire process.

        Args:
            csv_path (str): Path to CSV data file
            rsi_window (int): RSI calculation window
            RSI_oversoldLevel (float): RSI oversold threshold
            RSI_overboughtLevel (float): RSI overbought threshold
            bollinger_window (int): Bollinger Bands window
            bollinger_std_dev (float): Bollinger Bands standard deviation
            obv_window (int): OBV moving average window
            obv_threshold (float): OBV momentum threshold
            strategy_weights (dict): Strategy component weights
            initial_capital (float): Starting capital
            position_size (float): Fraction of capital to use per trade
            commission (float): Commission rate per trade

        Returns:
            dict: Comprehensive backtesting results
        """
        # Load and prepare data
        df = self.load_data(csv_path)

        # Calculate technical indicators
        df = self.calculate_technical_indicators(
            rsi_window=rsi_window,
            bollinger_window=bollinger_window,
            bollinger_std_dev=bollinger_std_dev,
            obv_window=obv_window
        )

        # Calculate strategy scores
        df = self.calculate_strategy_scores(
            df,
            RSI_oversoldLevel=RSI_oversoldLevel,
            RSI_overboughtLevel=RSI_overboughtLevel,
            obv_threshold=obv_threshold
        )

        # Generate trading signals
        df = self.generate_trading_signals(
            df,
            strategy_weights=strategy_weights
        )

        # Simulate trading
        df = self.simulate_trading(
            df, initial_capital, position_size, commission)

        # Calculate performance metrics
        performance_metrics = self.calculate_performance_metrics(
            df, initial_capital)

        # Store results
        self.results = df
        self.performance_metrics = performance_metrics

        return {
            'data': df,
            'performance_metrics': performance_metrics,
            'parameters': {
                'rsi_window': rsi_window,
                'RSI_oversoldLevel': RSI_oversoldLevel,
                'RSI_overboughtLevel': RSI_overboughtLevel,
                'bollinger_window': bollinger_window,
                'bollinger_std_dev': bollinger_std_dev,
                'obv_window': obv_window,
                'obv_threshold': obv_threshold,
                'strategy_weights': strategy_weights or {'rsi': 0.4, 'bollinger': 0.4, 'obv': 0.2},
                'initial_capital': initial_capital,
                'position_size': position_size,
                'commission': commission
            }
        }

    def create_visualization(self,
                             output_path: str = 'backtest_results.png',
                             figsize: Tuple[int, int] = (15, 12),
                             sample_points: int = 5000) -> None:
        """
        Create comprehensive visualization with 3 subplots.

        Args:
            output_path (str): Path to save the PNG file
            figsize (tuple): Figure size (width, height)
            sample_points (int): Number of points to sample for visualization
        """
        if self.results is None:
            raise ValueError(
                "No results to visualize. Run backtest_strategy() first.")

        df = self.results.copy()

        # Sample data if too large for visualization
        if len(df) > sample_points:
            step = len(df) // sample_points
            df = df.iloc[::step].copy()

        # Create figure with subplots
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=figsize, sharex=True)
        fig.suptitle('Finance Strategy Backtesting Results',
                     fontsize=16, fontweight='bold')

        # Subplot 1: Price Chart with Signals
        ax1.plot(df.index, df['close'],
                 label='Close Price', color='black', linewidth=1)

        # Plot buy signals (green up arrows)
        buy_signals = df[df['buy_signal'] == True]
        if len(buy_signals) > 0:
            ax1.scatter(buy_signals.index, buy_signals['close'],
                        marker='^', color='green', s=50, label='Buy Signal', zorder=5)

        # Plot sell signals (red down arrows)
        sell_signals = df[df['sell_signal'] == True]
        if len(sell_signals) > 0:
            ax1.scatter(sell_signals.index, sell_signals['close'],
                        marker='v', color='red', s=50, label='Sell Signal', zorder=5)

        ax1.set_ylabel('Price')
        ax1.set_title('Stock Price with Trading Signals')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Subplot 2: Strategy Scores
        ax2.plot(df.index, df['rsi_score'],
                 label='RSI Score', color='blue', alpha=0.7)
        ax2.plot(df.index, df['bb_score'],
                 label='Bollinger Score', color='orange', alpha=0.7)
        ax2.plot(df.index, df['obv_score'],
                 label='OBV Score', color='purple', alpha=0.7)
        ax2.plot(df.index, df['composite_score'],
                 label='Composite Score', color='red', linewidth=2)

        # Add threshold lines
        ax2.axhline(y=30, color='green', linestyle='--',
                    alpha=0.5, label='Buy Threshold')
        ax2.axhline(y=70, color='red', linestyle='--',
                    alpha=0.5, label='Sell Threshold')

        ax2.set_ylabel('Score')
        ax2.set_title('Strategy Scores Over Time')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0, 100)

        # Subplot 3: Equity Curve
        ax3.plot(df.index, df['portfolio_value'],
                 label='Portfolio Value', color='green', linewidth=2)
        ax3.axhline(y=df['portfolio_value'].iloc[0], color='gray',
                    linestyle='--', alpha=0.5, label='Initial Capital')

        ax3.set_ylabel('Portfolio Value ($)')
        ax3.set_xlabel('Date')
        ax3.set_title('Portfolio Equity Curve')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # Format x-axis
        ax3.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax3.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
        plt.setp(ax3.xaxis.get_majorticklabels(), rotation=45)

        # Adjust layout and save
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"Visualization saved to: {output_path}")

    def print_performance_summary(self) -> None:
        """Print a formatted summary of performance metrics."""
        if not self.performance_metrics:
            print("No performance metrics available. Run backtest_strategy() first.")
            return

        metrics = self.performance_metrics

        print("\n" + "="*60)
        print("BACKTESTING PERFORMANCE SUMMARY")
        print("="*60)

        print(f"Total Return: {metrics['total_return']:.2%}")
        print(f"Annualized Return: {metrics['annualized_return']:.2%}")
        print(f"Volatility (Annualized): {metrics['volatility']:.2%}")
        print(f"Sharpe Ratio: {metrics['sharpe_ratio']:.3f}")
        print(f"Maximum Drawdown: {metrics['max_drawdown']:.2%}")
        print(f"Final Portfolio Value: ${metrics['final_value']:,.2f}")

        print(f"\nTrading Statistics:")
        print(f"Total Trades: {metrics['total_trades']}")
        print(f"Winning Trades: {metrics['winning_trades']}")
        print(f"Losing Trades: {metrics['losing_trades']}")
        print(f"Win Rate: {metrics['win_rate']:.2%}")
        print(f"Average Win: {metrics['avg_win']:.4f}")
        print(f"Average Loss: {metrics['avg_loss']:.4f}")
        print(f"Profit Factor: {metrics['profit_factor']:.3f}")
        print(f"Trading Period: {metrics['trading_period_years']:.2f} years")

        print("="*60)
