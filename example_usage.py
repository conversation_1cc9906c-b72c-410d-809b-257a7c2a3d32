"""
Example Usage of the Finance Strategy Backtesting System

This script demonstrates how to use the comprehensive backtesting system
for finance strategies using technical indicators.
"""

from finance_backtesting import FinanceBacktester
import pandas as pd

def main():
    """Main function demonstrating the backtesting system usage."""
    
    # Initialize the backtester
    backtester = FinanceBacktester()
    
    # Example 1: Basic backtesting with default parameters
    print("="*80)
    print("EXAMPLE 1: Basic Backtesting with Default Parameters")
    print("="*80)
    
    try:
        results = backtester.backtest_strategy(
            csv_path="./data/full_data.csv"
        )
        
        # Print performance summary
        backtester.print_performance_summary()
        
        # Create visualization
        backtester.create_visualization('basic_backtest_results.png')
        
    except Exception as e:
        print(f"Error in basic backtesting: {e}")
    
    # Example 2: Custom parameters backtesting
    print("\n" + "="*80)
    print("EXAMPLE 2: Custom Parameters Backtesting")
    print("="*80)
    
    try:
        # Define custom strategy weights
        custom_weights = {
            'rsi': 0.5,        # Higher weight on RSI
            'bollinger': 0.3,  # Medium weight on Bollinger Bands
            'obv': 0.2         # Lower weight on OBV
        }
        
        results = backtester.backtest_strategy(
            csv_path='TXF1_Minute_2020-01-01_2025-07-04.csv',
            rsi_window=21,                    # Longer RSI window
            RSI_oversoldLevel=25,             # More aggressive oversold level
            RSI_overboughtLevel=75,           # More aggressive overbought level
            bollinger_window=30,              # Longer Bollinger Bands window
            bollinger_std_dev=2.5,            # Wider Bollinger Bands
            obv_window=15,                    # Longer OBV window
            obv_threshold=0.15,               # Higher OBV threshold
            strategy_weights=custom_weights,   # Custom weights
            initial_capital=200000,           # Higher initial capital
            position_size=0.8,                # More conservative position size
            commission=0.0015                 # Higher commission
        )
        
        # Print performance summary
        backtester.print_performance_summary()
        
        # Create visualization with custom settings
        backtester.create_visualization(
            output_path='custom_backtest_results.png',
            figsize=(18, 14),
            sample_points=3000
        )
        
    except Exception as e:
        print(f"Error in custom backtesting: {e}")
    
    # Example 3: Parameter comparison
    print("\n" + "="*80)
    print("EXAMPLE 3: Parameter Comparison Study")
    print("="*80)
    
    # Test different RSI windows
    rsi_windows = [10, 14, 21, 30]
    comparison_results = []
    
    for rsi_window in rsi_windows:
        try:
            print(f"\nTesting RSI window: {rsi_window}")
            
            # Create new backtester instance for each test
            test_backtester = FinanceBacktester()
            
            results = test_backtester.backtest_strategy(
                csv_path='TXF1_Minute_2020-01-01_2025-07-04.csv',
                rsi_window=rsi_window
            )
            
            # Store results for comparison
            comparison_results.append({
                'rsi_window': rsi_window,
                'total_return': results['performance_metrics']['total_return'],
                'sharpe_ratio': results['performance_metrics']['sharpe_ratio'],
                'max_drawdown': results['performance_metrics']['max_drawdown'],
                'win_rate': results['performance_metrics']['win_rate']
            })
            
        except Exception as e:
            print(f"Error testing RSI window {rsi_window}: {e}")
    
    # Display comparison results
    if comparison_results:
        print("\nRSI Window Comparison Results:")
        print("-" * 80)
        print(f"{'RSI Window':<12} {'Total Return':<15} {'Sharpe Ratio':<15} {'Max Drawdown':<15} {'Win Rate':<12}")
        print("-" * 80)
        
        for result in comparison_results:
            print(f"{result['rsi_window']:<12} "
                  f"{result['total_return']:<15.2%} "
                  f"{result['sharpe_ratio']:<15.3f} "
                  f"{result['max_drawdown']:<15.2%} "
                  f"{result['win_rate']:<12.2%}")
    
    # Example 4: Strategy weight optimization
    print("\n" + "="*80)
    print("EXAMPLE 4: Strategy Weight Optimization")
    print("="*80)
    
    weight_combinations = [
        {'rsi': 0.6, 'bollinger': 0.3, 'obv': 0.1},  # RSI-heavy
        {'rsi': 0.3, 'bollinger': 0.6, 'obv': 0.1},  # Bollinger-heavy
        {'rsi': 0.3, 'bollinger': 0.3, 'obv': 0.4},  # OBV-heavy
        {'rsi': 0.33, 'bollinger': 0.33, 'obv': 0.34}  # Equal weights
    ]
    
    weight_results = []
    
    for i, weights in enumerate(weight_combinations):
        try:
            print(f"\nTesting weight combination {i+1}: {weights}")
            
            test_backtester = FinanceBacktester()
            
            results = test_backtester.backtest_strategy(
                csv_path='TXF1_Minute_2020-01-01_2025-07-04.csv',
                strategy_weights=weights
            )
            
            weight_results.append({
                'weights': weights,
                'total_return': results['performance_metrics']['total_return'],
                'sharpe_ratio': results['performance_metrics']['sharpe_ratio'],
                'max_drawdown': results['performance_metrics']['max_drawdown']
            })
            
        except Exception as e:
            print(f"Error testing weight combination {i+1}: {e}")
    
    # Display weight optimization results
    if weight_results:
        print("\nStrategy Weight Optimization Results:")
        print("-" * 100)
        print(f"{'Strategy Weights':<40} {'Total Return':<15} {'Sharpe Ratio':<15} {'Max Drawdown':<15}")
        print("-" * 100)
        
        for result in weight_results:
            weights_str = f"RSI:{result['weights']['rsi']:.1f} BB:{result['weights']['bollinger']:.1f} OBV:{result['weights']['obv']:.1f}"
            print(f"{weights_str:<40} "
                  f"{result['total_return']:<15.2%} "
                  f"{result['sharpe_ratio']:<15.3f} "
                  f"{result['max_drawdown']:<15.2%}")
    
    print("\n" + "="*80)
    print("BACKTESTING EXAMPLES COMPLETED")
    print("="*80)
    print("Check the generated PNG files for visualizations:")
    print("- basic_backtest_results.png")
    print("- custom_backtest_results.png")

if __name__ == "__main__":
    main()
