"""
Comprehensive Example of the Finance Strategy Backtesting System

This script demonstrates the complete functionality of the backtesting system
with various parameter configurations and optimizations for larger datasets.
"""

from finance_backtesting_manual import FinanceBacktester
import pandas as pd
import time

def create_optimized_sample(csv_path: str, sample_size: int = 10000) -> str:
    """
    Create an optimized sample from the full dataset.
    
    Args:
        csv_path (str): Path to the full CSV file
        sample_size (int): Number of rows to sample
        
    Returns:
        str: Path to the sample file
    """
    print(f"Creating optimized sample of {sample_size} rows...")
    
    # Read the full dataset
    df = pd.read_csv(csv_path)
    
    # Take evenly spaced samples to maintain time series structure
    step = len(df) // sample_size
    if step < 1:
        step = 1
    
    sample_df = df.iloc[::step].head(sample_size)
    
    sample_path = f'optimized_sample_{sample_size}.csv'
    sample_df.to_csv(sample_path, index=False)
    
    print(f"Sample created: {len(sample_df)} rows from {sample_df['datetime'].iloc[0]} to {sample_df['datetime'].iloc[-1]}")
    return sample_path

def run_basic_backtest():
    """Run a basic backtest with default parameters."""
    print("\n" + "="*80)
    print("BASIC BACKTEST WITH DEFAULT PARAMETERS")
    print("="*80)
    
    # Create sample data
    sample_path = create_optimized_sample('TXF1_Minute_2020-01-01_2025-07-04.csv', 5000)
    
    # Initialize backtester
    backtester = FinanceBacktester()
    
    # Run backtest
    start_time = time.time()
    results = backtester.backtest_strategy(csv_path=sample_path)
    end_time = time.time()
    
    print(f"Backtest completed in {end_time - start_time:.2f} seconds")
    
    # Print results
    backtester.print_performance_summary()
    
    # Create visualization
    backtester.create_visualization('basic_comprehensive_results.png')
    
    return results

def run_custom_backtest():
    """Run a backtest with custom parameters."""
    print("\n" + "="*80)
    print("CUSTOM BACKTEST WITH OPTIMIZED PARAMETERS")
    print("="*80)
    
    # Create sample data
    sample_path = create_optimized_sample('TXF1_Minute_2020-01-01_2025-07-04.csv', 5000)
    
    # Initialize backtester
    backtester = FinanceBacktester()
    
    # Custom strategy weights (more emphasis on RSI and Bollinger Bands)
    custom_weights = {
        'rsi': 0.5,
        'bollinger': 0.4,
        'obv': 0.1
    }
    
    # Run backtest with custom parameters
    start_time = time.time()
    results = backtester.backtest_strategy(
        csv_path=sample_path,
        rsi_window=21,                    # Longer RSI window for smoother signals
        RSI_oversoldLevel=25,             # More aggressive oversold threshold
        RSI_overboughtLevel=75,           # More aggressive overbought threshold
        bollinger_window=25,              # Longer Bollinger Bands window
        bollinger_std_dev=2.2,            # Slightly wider bands
        obv_window=20,                    # Longer OBV smoothing
        obv_threshold=0.12,               # Higher OBV threshold
        strategy_weights=custom_weights,   # Custom weights
        initial_capital=100000,           # Standard capital
        position_size=0.9,                # Aggressive position sizing
        commission=0.001                  # Standard commission
    )
    end_time = time.time()
    
    print(f"Custom backtest completed in {end_time - start_time:.2f} seconds")
    
    # Print results
    backtester.print_performance_summary()
    
    # Create visualization
    backtester.create_visualization('custom_comprehensive_results.png')
    
    return results

def run_parameter_comparison():
    """Compare different parameter configurations."""
    print("\n" + "="*80)
    print("PARAMETER COMPARISON STUDY")
    print("="*80)
    
    # Create sample data
    sample_path = create_optimized_sample('TXF1_Minute_2020-01-01_2025-07-04.csv', 3000)
    
    # Test different RSI windows
    rsi_windows = [10, 14, 21, 30]
    results = []
    
    for rsi_window in rsi_windows:
        print(f"\nTesting RSI window: {rsi_window}")
        
        backtester = FinanceBacktester()
        
        start_time = time.time()
        result = backtester.backtest_strategy(
            csv_path=sample_path,
            rsi_window=rsi_window
        )
        end_time = time.time()
        
        results.append({
            'rsi_window': rsi_window,
            'total_return': result['performance_metrics']['total_return'],
            'sharpe_ratio': result['performance_metrics']['sharpe_ratio'],
            'max_drawdown': result['performance_metrics']['max_drawdown'],
            'win_rate': result['performance_metrics']['win_rate'],
            'execution_time': end_time - start_time
        })
    
    # Display comparison results
    print("\n" + "-"*90)
    print(f"{'RSI Window':<12} {'Total Return':<15} {'Sharpe Ratio':<15} {'Max Drawdown':<15} {'Win Rate':<12} {'Time (s)':<10}")
    print("-"*90)
    
    for result in results:
        print(f"{result['rsi_window']:<12} "
              f"{result['total_return']:<15.2%} "
              f"{result['sharpe_ratio']:<15.3f} "
              f"{result['max_drawdown']:<15.2%} "
              f"{result['win_rate']:<12.2%} "
              f"{result['execution_time']:<10.2f}")
    
    return results

def run_strategy_weight_optimization():
    """Test different strategy weight combinations."""
    print("\n" + "="*80)
    print("STRATEGY WEIGHT OPTIMIZATION")
    print("="*80)
    
    # Create sample data
    sample_path = create_optimized_sample('TXF1_Minute_2020-01-01_2025-07-04.csv', 3000)
    
    weight_combinations = [
        {'rsi': 0.7, 'bollinger': 0.2, 'obv': 0.1},   # RSI-heavy
        {'rsi': 0.2, 'bollinger': 0.7, 'obv': 0.1},   # Bollinger-heavy
        {'rsi': 0.2, 'bollinger': 0.2, 'obv': 0.6},   # OBV-heavy
        {'rsi': 0.33, 'bollinger': 0.33, 'obv': 0.34}, # Equal weights
        {'rsi': 0.5, 'bollinger': 0.4, 'obv': 0.1}    # Balanced
    ]
    
    results = []
    
    for i, weights in enumerate(weight_combinations):
        print(f"\nTesting weight combination {i+1}: {weights}")
        
        backtester = FinanceBacktester()
        
        start_time = time.time()
        result = backtester.backtest_strategy(
            csv_path=sample_path,
            strategy_weights=weights
        )
        end_time = time.time()
        
        results.append({
            'weights': weights,
            'total_return': result['performance_metrics']['total_return'],
            'sharpe_ratio': result['performance_metrics']['sharpe_ratio'],
            'max_drawdown': result['performance_metrics']['max_drawdown'],
            'execution_time': end_time - start_time
        })
    
    # Display optimization results
    print("\n" + "-"*100)
    print(f"{'Strategy Weights':<40} {'Total Return':<15} {'Sharpe Ratio':<15} {'Max Drawdown':<15} {'Time (s)':<10}")
    print("-"*100)
    
    for result in results:
        weights_str = f"RSI:{result['weights']['rsi']:.1f} BB:{result['weights']['bollinger']:.1f} OBV:{result['weights']['obv']:.1f}"
        print(f"{weights_str:<40} "
              f"{result['total_return']:<15.2%} "
              f"{result['sharpe_ratio']:<15.3f} "
              f"{result['max_drawdown']:<15.2%} "
              f"{result['execution_time']:<10.2f}")
    
    return results

def main():
    """Main function to run comprehensive examples."""
    print("COMPREHENSIVE FINANCE STRATEGY BACKTESTING SYSTEM")
    print("="*80)
    print("This example demonstrates the full functionality of the backtesting system")
    print("with optimized performance for larger datasets.")
    
    try:
        # Run basic backtest
        basic_results = run_basic_backtest()
        
        # Run custom backtest
        custom_results = run_custom_backtest()
        
        # Run parameter comparison
        param_results = run_parameter_comparison()
        
        # Run strategy weight optimization
        weight_results = run_strategy_weight_optimization()
        
        print("\n" + "="*80)
        print("ALL BACKTESTING EXAMPLES COMPLETED SUCCESSFULLY!")
        print("="*80)
        print("Generated files:")
        print("- basic_comprehensive_results.png")
        print("- custom_comprehensive_results.png")
        print("- optimized_sample_*.csv (sample data files)")
        
        print("\nThe system successfully demonstrated:")
        print("✓ Basic backtesting with default parameters")
        print("✓ Custom parameter configuration")
        print("✓ Parameter comparison and optimization")
        print("✓ Strategy weight optimization")
        print("✓ Performance visualization")
        print("✓ Comprehensive performance metrics")
        
    except Exception as e:
        print(f"Error during comprehensive testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
