"""
Quick test with a smaller dataset
"""

import pandas as pd
from finance_backtesting_manual import FinanceBacktester

def create_sample_data():
    """Create a small sample dataset for testing."""
    # Read the first 1000 rows for testing
    df = pd.read_csv('TXF1_Minute_2020-01-01_2025-07-04.csv', nrows=1000)
    df.to_csv('sample_data.csv', index=False)
    print(f"Created sample data with {len(df)} rows")

def main():
    """Test with smaller dataset."""
    
    print("Creating sample data...")
    create_sample_data()
    
    print("\nTesting with sample data...")
    
    try:
        # Initialize the backtester
        backtester = FinanceBacktester()
        
        # Run a simple backtest
        print("Running backtest with sample data...")
        results = backtester.backtest_strategy(
            csv_path='sample_data.csv'
        )
        
        # Print performance summary
        backtester.print_performance_summary()
        
        # Create visualization
        print("\nCreating visualization...")
        backtester.create_visualization('sample_backtest_results.png')
        
        print("\nBacktest completed successfully!")
        print("Check 'sample_backtest_results.png' for the visualization.")
        
    except Exception as e:
        print(f"Error during backtesting: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
