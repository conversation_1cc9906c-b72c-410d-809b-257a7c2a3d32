"""
Fix for pandas-ta compatibility issue with newer numpy versions

This script provides a workaround for the pandas-ta import error:
ImportError: cannot import name 'NaN' from 'numpy'

The issue occurs because pandas-ta tries to import 'NaN' from numpy,
but in newer numpy versions it's called 'nan'.
"""

import sys
import os

def fix_pandas_ta_compatibility():
    """
    Apply a monkey patch to fix pandas-ta compatibility with newer numpy versions.
    """
    try:
        import numpy as np
        
        # Add NaN as an alias for nan if it doesn't exist
        if not hasattr(np, 'NaN'):
            np.NaN = np.nan
            print("✓ Applied numpy compatibility fix for pandas-ta")
        
        # Now try to import pandas-ta
        import pandas_ta as ta
        print("✓ pandas-ta imported successfully")
        return True
        
    except ImportError as e:
        print(f"✗ pandas-ta import still failing: {e}")
        return False

def check_system_compatibility():
    """Check system compatibility and suggest solutions."""
    print("PANDAS-TA COMPATIBILITY CHECKER")
    print("="*50)
    
    # Check numpy version
    try:
        import numpy as np
        print(f"✓ NumPy version: {np.__version__}")
    except ImportError:
        print("✗ NumPy not installed")
        return False
    
    # Check pandas version
    try:
        import pandas as pd
        print(f"✓ Pandas version: {pd.__version__}")
    except ImportError:
        print("✗ Pandas not installed")
        return False
    
    # Check pandas-ta
    try:
        # Try the fix first
        if not hasattr(np, 'NaN'):
            np.NaN = np.nan
        
        import pandas_ta as ta
        print(f"✓ pandas-ta version: {ta.__version__}")
        print("✓ pandas-ta is working correctly")
        return True
        
    except ImportError as e:
        print(f"✗ pandas-ta import error: {e}")
        print("\nSUGGESTED SOLUTIONS:")
        print("1. Use the manual implementation (finance_backtesting_manual.py)")
        print("2. Downgrade numpy: pip install numpy==1.21.0")
        print("3. Use a different pandas-ta version: pip install pandas-ta==0.3.14b")
        print("4. Use the compatibility fix in this script")
        return False

def create_fixed_import_module():
    """Create a module that fixes the import issue."""
    fix_code = '''"""
Fixed pandas-ta import module
"""
import numpy as np

# Fix the NaN import issue
if not hasattr(np, 'NaN'):
    np.NaN = np.nan

# Now import pandas-ta
import pandas_ta as ta

# Re-export everything
from pandas_ta import *
'''
    
    with open('pandas_ta_fixed.py', 'w') as f:
        f.write(fix_code)
    
    print("✓ Created pandas_ta_fixed.py module")
    print("  You can now use: from pandas_ta_fixed import *")

def main():
    """Main function to check and fix pandas-ta compatibility."""
    print("PANDAS-TA COMPATIBILITY FIX")
    print("="*40)
    
    # Check current compatibility
    if check_system_compatibility():
        print("\n✓ Your system is already compatible!")
        return
    
    print("\nATTEMPTING FIXES...")
    print("-" * 30)
    
    # Try the monkey patch fix
    if fix_pandas_ta_compatibility():
        print("✓ Compatibility fix successful!")
        print("\nTo use this fix in your code, add this at the top:")
        print("import numpy as np")
        print("if not hasattr(np, 'NaN'): np.NaN = np.nan")
        print("import pandas_ta as ta")
    else:
        print("✗ Compatibility fix failed")
        print("\nRECOMMENDED SOLUTION:")
        print("Use the manual implementation instead:")
        print("from finance_backtesting_manual import FinanceBacktester")
        
        # Create the fixed module
        create_fixed_import_module()
    
    print("\nALTERNATIVE SOLUTIONS:")
    print("1. Use finance_backtesting_manual.py (recommended)")
    print("2. Downgrade numpy: pip install numpy==1.21.0")
    print("3. Try different pandas-ta: pip install pandas-ta==0.3.14b")
    print("4. Use TA-Lib instead: pip install TA-Lib")

if __name__ == "__main__":
    main()
