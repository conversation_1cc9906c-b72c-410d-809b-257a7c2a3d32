"""
Test script for the manual backtesting system
"""

from finance_backtesting_manual import FinanceBacktester

def main():
    """Test the manual backtesting system."""
    
    print("Testing Manual Finance Backtesting System")
    print("="*50)
    
    try:
        # Initialize the backtester
        backtester = FinanceBacktester()
        
        # Run a simple backtest
        print("Running backtest with default parameters...")
        results = backtester.backtest_strategy(
            csv_path='TXF1_Minute_2020-01-01_2025-07-04.csv'
        )
        
        # Print performance summary
        backtester.print_performance_summary()
        
        # Create visualization
        print("\nCreating visualization...")
        backtester.create_visualization('manual_backtest_results.png')
        
        print("\nBacktest completed successfully!")
        print("Check 'manual_backtest_results.png' for the visualization.")
        
    except Exception as e:
        print(f"Error during backtesting: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
