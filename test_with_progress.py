"""
Test script for the enhanced backtesting system with progress bars and results folder
"""

from finance_backtesting_manual import FinanceBacktester
import pandas as pd
import time
from datetime import datetime

def create_sample_data(size: int = 2000):
    """Create a sample dataset for testing."""
    print(f"Creating sample data with {size} rows...")
    df = pd.read_csv('TXF1_Minute_2020-01-01_2025-07-04.csv', nrows=size)
    sample_filename = f'sample_data_{size}.csv'
    df.to_csv(sample_filename, index=False)
    print(f"Sample data saved to: {sample_filename}")
    return sample_filename

def test_basic_backtest():
    """Test basic backtesting with progress bars."""
    print("\n" + "="*80)
    print("TESTING BASIC BACKTEST WITH PROGRESS BARS")
    print("="*80)
    
    # Create sample data
    sample_file = create_sample_data(2000)
    
    # Initialize backtester
    backtester = FinanceBacktester()
    
    # Run backtest
    print("\nRunning backtest...")
    start_time = time.time()
    
    results = backtester.backtest_strategy(
        csv_path=sample_file,
        initial_capital=100000
    )
    
    end_time = time.time()
    print(f"\nBacktest completed in {end_time - start_time:.2f} seconds")
    
    # Print performance summary
    backtester.print_performance_summary()
    
    # Save results to files
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    print("\nSaving results...")
    backtester.create_visualization(f'basic_backtest_{timestamp}.png')
    backtester.save_results_to_csv(f'basic_results_{timestamp}.csv')
    backtester.save_performance_metrics(f'basic_metrics_{timestamp}.txt')
    
    return results

def test_custom_backtest():
    """Test custom backtesting with different parameters."""
    print("\n" + "="*80)
    print("TESTING CUSTOM BACKTEST WITH DIFFERENT PARAMETERS")
    print("="*80)
    
    # Create sample data
    sample_file = create_sample_data(1500)
    
    # Initialize backtester
    backtester = FinanceBacktester()
    
    # Custom parameters
    custom_weights = {
        'rsi': 0.6,        # Higher weight on RSI
        'bollinger': 0.3,  # Medium weight on Bollinger Bands
        'obv': 0.1         # Lower weight on OBV
    }
    
    print("\nRunning custom backtest...")
    start_time = time.time()
    
    results = backtester.backtest_strategy(
        csv_path=sample_file,
        rsi_window=21,                    # Longer RSI window
        RSI_oversoldLevel=25,             # More aggressive oversold level
        RSI_overboughtLevel=75,           # More aggressive overbought level
        bollinger_window=30,              # Longer Bollinger Bands window
        bollinger_std_dev=2.5,            # Wider Bollinger Bands
        obv_window=15,                    # Longer OBV window
        obv_threshold=0.15,               # Higher OBV threshold
        strategy_weights=custom_weights,   # Custom weights
        initial_capital=150000,           # Higher initial capital
        position_size=0.8,                # More conservative position size
        commission=0.0015                 # Higher commission
    )
    
    end_time = time.time()
    print(f"\nCustom backtest completed in {end_time - start_time:.2f} seconds")
    
    # Print performance summary
    backtester.print_performance_summary()
    
    # Save results to files
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    print("\nSaving custom results...")
    backtester.create_visualization(f'custom_backtest_{timestamp}.png')
    backtester.save_results_to_csv(f'custom_results_{timestamp}.csv')
    backtester.save_performance_metrics(f'custom_metrics_{timestamp}.txt')
    
    return results

def test_parameter_comparison():
    """Test different RSI windows with progress tracking."""
    print("\n" + "="*80)
    print("TESTING PARAMETER COMPARISON")
    print("="*80)
    
    # Create sample data
    sample_file = create_sample_data(1000)
    
    # Test different RSI windows
    rsi_windows = [10, 14, 21, 30]
    comparison_results = []
    
    for rsi_window in rsi_windows:
        print(f"\n--- Testing RSI window: {rsi_window} ---")
        
        # Create new backtester instance for each test
        backtester = FinanceBacktester()
        
        start_time = time.time()
        results = backtester.backtest_strategy(
            csv_path=sample_file,
            rsi_window=rsi_window
        )
        end_time = time.time()
        
        # Store results for comparison
        comparison_results.append({
            'rsi_window': rsi_window,
            'total_return': results['performance_metrics']['total_return'],
            'sharpe_ratio': results['performance_metrics']['sharpe_ratio'],
            'max_drawdown': results['performance_metrics']['max_drawdown'],
            'win_rate': results['performance_metrics']['win_rate'],
            'execution_time': end_time - start_time
        })
        
        # Save individual results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backtester.create_visualization(f'rsi_{rsi_window}_backtest_{timestamp}.png')
        backtester.save_performance_metrics(f'rsi_{rsi_window}_metrics_{timestamp}.txt')
    
    # Display comparison results
    print("\n" + "="*90)
    print("RSI WINDOW COMPARISON RESULTS")
    print("="*90)
    print(f"{'RSI Window':<12} {'Total Return':<15} {'Sharpe Ratio':<15} {'Max Drawdown':<15} {'Win Rate':<12} {'Time (s)':<10}")
    print("-"*90)
    
    for result in comparison_results:
        print(f"{result['rsi_window']:<12} "
              f"{result['total_return']:<15.2%} "
              f"{result['sharpe_ratio']:<15.3f} "
              f"{result['max_drawdown']:<15.2%} "
              f"{result['win_rate']:<12.2%} "
              f"{result['execution_time']:<10.2f}")
    
    return comparison_results

def main():
    """Main function to run all tests."""
    print("ENHANCED FINANCE BACKTESTING SYSTEM TEST")
    print("="*80)
    print("Testing system with progress bars and results folder functionality")
    
    try:
        # Test basic backtest
        basic_results = test_basic_backtest()
        
        # Test custom backtest
        custom_results = test_custom_backtest()
        
        # Test parameter comparison
        comparison_results = test_parameter_comparison()
        
        print("\n" + "="*80)
        print("ALL TESTS COMPLETED SUCCESSFULLY!")
        print("="*80)
        print("Features tested:")
        print("✓ Progress bars during backtesting")
        print("✓ Results folder creation and organization")
        print("✓ Automatic file saving with timestamps")
        print("✓ CSV results export")
        print("✓ Performance metrics text export")
        print("✓ Visualization PNG export")
        print("✓ Parameter comparison studies")
        
        print(f"\nCheck the 'results' folder for all generated files.")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
