from finance_backtesting_manual import FinanceBacktester
import pandas as pd


def create_sample_data(csv_path: str, sample_size: int = 5000) -> str:
    """Create a sample from the full dataset for faster processing."""
    print(f"Creating sample of {sample_size} rows from {csv_path}...")

    try:
        # Read the full dataset
        df = pd.read_csv(csv_path)
        print(f"Original dataset: {len(df)} rows")

        # Take evenly spaced samples to maintain time series structure
        step = len(df) // sample_size
        if step < 1:
            step = 1

        sample_df = df.iloc[::step].head(sample_size)

        sample_path = 'sample_for_backtest.csv'
        sample_df.to_csv(sample_path, index=False)

        print(
            f"Sample created: {len(sample_df)} rows from {sample_df['datetime'].iloc[0]} to {sample_df['datetime'].iloc[-1]}")
        return sample_path

    except FileNotFoundError:
        print(f"File not found: {csv_path}")
        print("Using the available TXF data instead...")
        return 'TXF1_Minute_2020-01-01_2025-07-04.csv'


def main():
    """Main function to run the backtesting system."""
    print("FINANCE STRATEGY BACKTESTING SYSTEM")
    print("="*50)

    # Initialize backtester (creates results folder automatically)
    backtester = FinanceBacktester()

    # Try to use the specified data file, fallback to available data
    try:
        data_file = './data/full_data.csv'
        sample_file = create_sample_data(data_file, 5000)
    except:
        print("Using available TXF data...")
        sample_file = create_sample_data(
            'TXF1_Minute_2020-01-01_2025-07-04.csv', 5000)

    print(f"\nRunning backtest on: {sample_file}")

    # Run backtest with progress bars
    results = backtester.backtest_strategy(sample_file)

    # Print performance summary
    backtester.print_performance_summary()

    # Create visualization and save results
    print("\nSaving results...")
    backtester.create_visualization('backtest_results.png')
    backtester.save_results_to_csv('backtest_data.csv')
    backtester.save_performance_metrics('performance_summary.txt')

    print("\nBacktest completed! Check the 'results' folder for all output files.")


if __name__ == "__main__":
    main()
