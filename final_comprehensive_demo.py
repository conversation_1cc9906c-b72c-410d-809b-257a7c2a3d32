"""
Final Comprehensive Demo of the Enhanced Finance Strategy Backtesting System

This script demonstrates all features including:
- Progress bars with tqdm
- Automatic results folder creation
- Timestamped file saving
- CSV and text export functionality
- Visualization with organized file structure
"""

from finance_backtesting_manual import FinanceBacktester
import pandas as pd
import time
from datetime import datetime
import os

def create_optimized_sample(csv_path: str, sample_size: int = 3000) -> str:
    """Create an optimized sample from the full dataset."""
    print(f"Creating optimized sample of {sample_size} rows...")
    
    # Read the full dataset
    df = pd.read_csv(csv_path)
    
    # Take evenly spaced samples to maintain time series structure
    step = len(df) // sample_size
    if step < 1:
        step = 1
    
    sample_df = df.iloc[::step].head(sample_size)
    
    sample_path = f'optimized_sample_{sample_size}.csv'
    sample_df.to_csv(sample_path, index=False)
    
    print(f"Sample created: {len(sample_df)} rows from {sample_df['datetime'].iloc[0]} to {sample_df['datetime'].iloc[-1]}")
    return sample_path

def run_comprehensive_backtest():
    """Run a comprehensive backtest demonstrating all features."""
    print("\n" + "="*80)
    print("COMPREHENSIVE BACKTEST WITH ALL ENHANCED FEATURES")
    print("="*80)
    
    # Create optimized sample data
    sample_path = create_optimized_sample('TXF1_Minute_2020-01-01_2025-07-04.csv', 5000)
    
    # Initialize backtester (automatically creates results folder)
    backtester = FinanceBacktester()
    
    # Custom strategy configuration
    strategy_config = {
        'rsi_window': 18,
        'RSI_oversoldLevel': 28,
        'RSI_overboughtLevel': 72,
        'bollinger_window': 22,
        'bollinger_std_dev': 2.1,
        'obv_window': 12,
        'obv_threshold': 0.08,
        'strategy_weights': {
            'rsi': 0.45,
            'bollinger': 0.35,
            'obv': 0.20
        },
        'initial_capital': 200000,
        'position_size': 0.85,
        'commission': 0.0012
    }
    
    print("\nStrategy Configuration:")
    for key, value in strategy_config.items():
        print(f"  {key}: {value}")
    
    print(f"\nRunning comprehensive backtest...")
    start_time = time.time()
    
    # Run backtest with progress bars
    results = backtester.backtest_strategy(
        csv_path=sample_path,
        **strategy_config
    )
    
    end_time = time.time()
    execution_time = end_time - start_time
    print(f"\nBacktest completed in {execution_time:.2f} seconds")
    
    # Print performance summary
    backtester.print_performance_summary()
    
    # Generate timestamp for file naming
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    print(f"\nSaving comprehensive results with timestamp: {timestamp}")
    
    # Save all types of results
    print("Saving files...")
    
    # 1. Create visualization
    viz_filename = f'comprehensive_backtest_{timestamp}.png'
    backtester.create_visualization(viz_filename, figsize=(18, 14))
    
    # 2. Save detailed results to CSV
    csv_filename = f'comprehensive_results_{timestamp}.csv'
    backtester.save_results_to_csv(csv_filename)
    
    # 3. Save performance metrics to text file
    metrics_filename = f'comprehensive_metrics_{timestamp}.txt'
    backtester.save_performance_metrics(metrics_filename)
    
    # 4. Create a summary report
    summary_filename = f'comprehensive_summary_{timestamp}.txt'
    create_summary_report(backtester, strategy_config, execution_time, summary_filename)
    
    return results, timestamp

def create_summary_report(backtester, config, execution_time, filename):
    """Create a comprehensive summary report."""
    filepath = os.path.join(backtester.results_folder, filename)
    
    with open(filepath, 'w') as f:
        f.write("COMPREHENSIVE BACKTESTING SUMMARY REPORT\n")
        f.write("="*80 + "\n\n")
        
        f.write(f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Execution Time: {execution_time:.2f} seconds\n\n")
        
        f.write("STRATEGY CONFIGURATION:\n")
        f.write("-" * 40 + "\n")
        for key, value in config.items():
            f.write(f"{key}: {value}\n")
        f.write("\n")
        
        f.write("PERFORMANCE METRICS:\n")
        f.write("-" * 40 + "\n")
        metrics = backtester.performance_metrics
        f.write(f"Total Return: {metrics['total_return']:.2%}\n")
        f.write(f"Annualized Return: {metrics['annualized_return']:.2%}\n")
        f.write(f"Volatility: {metrics['volatility']:.2%}\n")
        f.write(f"Sharpe Ratio: {metrics['sharpe_ratio']:.3f}\n")
        f.write(f"Maximum Drawdown: {metrics['max_drawdown']:.2%}\n")
        f.write(f"Final Portfolio Value: ${metrics['final_value']:,.2f}\n")
        f.write(f"Win Rate: {metrics['win_rate']:.2%}\n")
        f.write(f"Total Trades: {metrics['total_trades']}\n")
        f.write(f"Profit Factor: {metrics['profit_factor']:.3f}\n\n")
        
        f.write("SYSTEM FEATURES DEMONSTRATED:\n")
        f.write("-" * 40 + "\n")
        f.write("✓ Progress bars during backtesting\n")
        f.write("✓ Automatic results folder creation\n")
        f.write("✓ Timestamped file naming\n")
        f.write("✓ CSV data export\n")
        f.write("✓ Performance metrics text export\n")
        f.write("✓ High-quality visualization export\n")
        f.write("✓ Comprehensive summary reporting\n")
        f.write("✓ Modular and extensible architecture\n")
    
    print(f"Summary report saved to: {filepath}")

def run_strategy_optimization():
    """Demonstrate strategy optimization with progress tracking."""
    print("\n" + "="*80)
    print("STRATEGY OPTIMIZATION WITH PROGRESS TRACKING")
    print("="*80)
    
    # Create sample data for optimization
    sample_path = create_optimized_sample('TXF1_Minute_2020-01-01_2025-07-04.csv', 2000)
    
    # Define parameter combinations to test
    test_combinations = [
        {'rsi_window': 14, 'bollinger_window': 20, 'weights': {'rsi': 0.5, 'bollinger': 0.4, 'obv': 0.1}},
        {'rsi_window': 21, 'bollinger_window': 25, 'weights': {'rsi': 0.4, 'bollinger': 0.5, 'obv': 0.1}},
        {'rsi_window': 18, 'bollinger_window': 22, 'weights': {'rsi': 0.45, 'bollinger': 0.35, 'obv': 0.2}},
        {'rsi_window': 16, 'bollinger_window': 24, 'weights': {'rsi': 0.6, 'bollinger': 0.25, 'obv': 0.15}},
    ]
    
    optimization_results = []
    
    for i, combo in enumerate(test_combinations, 1):
        print(f"\n--- Testing Combination {i}/{len(test_combinations)} ---")
        print(f"RSI Window: {combo['rsi_window']}, Bollinger Window: {combo['bollinger_window']}")
        print(f"Weights: {combo['weights']}")
        
        # Create new backtester for each test
        backtester = FinanceBacktester()
        
        start_time = time.time()
        results = backtester.backtest_strategy(
            csv_path=sample_path,
            rsi_window=combo['rsi_window'],
            bollinger_window=combo['bollinger_window'],
            strategy_weights=combo['weights']
        )
        end_time = time.time()
        
        # Store results
        optimization_results.append({
            'combination': i,
            'rsi_window': combo['rsi_window'],
            'bollinger_window': combo['bollinger_window'],
            'weights': combo['weights'],
            'total_return': results['performance_metrics']['total_return'],
            'sharpe_ratio': results['performance_metrics']['sharpe_ratio'],
            'max_drawdown': results['performance_metrics']['max_drawdown'],
            'win_rate': results['performance_metrics']['win_rate'],
            'execution_time': end_time - start_time
        })
        
        # Save individual results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backtester.create_visualization(f'optimization_{i}_{timestamp}.png')
        backtester.save_performance_metrics(f'optimization_{i}_metrics_{timestamp}.txt')
    
    # Display optimization results
    print("\n" + "="*100)
    print("STRATEGY OPTIMIZATION RESULTS")
    print("="*100)
    print(f"{'Combo':<6} {'RSI Win':<8} {'BB Win':<7} {'RSI Weight':<11} {'Total Return':<13} {'Sharpe':<8} {'Max DD':<8} {'Win Rate':<9} {'Time':<6}")
    print("-"*100)
    
    for result in optimization_results:
        weights = result['weights']
        print(f"{result['combination']:<6} "
              f"{result['rsi_window']:<8} "
              f"{result['bollinger_window']:<7} "
              f"{weights['rsi']:.1f}/{weights['bollinger']:.1f}/{weights['obv']:.1f}"
              f"{'':>4} "
              f"{result['total_return']:<13.2%} "
              f"{result['sharpe_ratio']:<8.3f} "
              f"{result['max_drawdown']:<8.2%} "
              f"{result['win_rate']:<9.2%} "
              f"{result['execution_time']:<6.2f}")
    
    return optimization_results

def main():
    """Main function demonstrating the complete enhanced system."""
    print("ENHANCED FINANCE STRATEGY BACKTESTING SYSTEM")
    print("="*80)
    print("Comprehensive demonstration of all enhanced features:")
    print("• Progress bars with tqdm")
    print("• Automatic results folder creation and organization")
    print("• Timestamped file naming for easy tracking")
    print("• Multiple export formats (PNG, CSV, TXT)")
    print("• Comprehensive reporting and analysis")
    
    try:
        # Run comprehensive backtest
        results, timestamp = run_comprehensive_backtest()
        
        # Run strategy optimization
        optimization_results = run_strategy_optimization()
        
        print("\n" + "="*80)
        print("ALL DEMONSTRATIONS COMPLETED SUCCESSFULLY!")
        print("="*80)
        
        print("\nEnhanced Features Demonstrated:")
        print("✓ Real-time progress tracking with tqdm progress bars")
        print("✓ Automatic results folder creation and management")
        print("✓ Timestamped file naming for organized results")
        print("✓ Multiple export formats (PNG visualizations, CSV data, TXT metrics)")
        print("✓ Comprehensive summary reporting")
        print("✓ Strategy optimization with progress tracking")
        print("✓ Modular and extensible system architecture")
        
        print(f"\nAll results saved in the 'results' folder with timestamp: {timestamp}")
        print("Files include:")
        print("• High-quality PNG visualizations")
        print("• Detailed CSV data exports")
        print("• Performance metrics text files")
        print("• Comprehensive summary reports")
        
        # Show results folder contents
        results_files = os.listdir('results')
        print(f"\nTotal files in results folder: {len(results_files)}")
        
    except Exception as e:
        print(f"Error during demonstration: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
